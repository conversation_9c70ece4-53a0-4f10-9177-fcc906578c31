import { api } from '../utils/api'
import {
  Order,
  OrderListItem,
  OrderListResult,
  OrderQueryParams
} from '../types/order'

export const orderApi = {
  // Get all orders with filtering and pagination
  getOrders: async (params?: OrderQueryParams): Promise<OrderListResult> => {
    const searchParams = new URLSearchParams()

    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.search) searchParams.append('search', params.search)
    if (params?.status) searchParams.append('status', params.status)
    if (params?.payment_status) searchParams.append('payment_status', params.payment_status)
    if (params?.customer_id) searchParams.append('customer_id', params.customer_id)
    if (params?.channel) searchParams.append('channel', params.channel)
    if (params?.sort_by) searchParams.append('sort_by', params.sort_by)
    if (params?.sort_order) searchParams.append('sort_order', params.sort_order)
    if (params?.date_range?.start) searchParams.append('date_start', params.date_range.start)
    if (params?.date_range?.end) searchParams.append('date_end', params.date_range.end)

    const queryString = searchParams.toString()
    const url = `/api/orders${queryString ? `?${queryString}` : ''}`

    const response = await api.get(url)
    return response.data
  },

  // Get a single order by ID
  getOrder: async (id: string): Promise<Order> => {
    const response = await api.get(`/api/orders/${id}`)
    return response.data
  },

  // Update order status
  updateOrderStatus: async (id: string, status: string): Promise<Order> => {
    const response = await api.patch(`/api/orders/${id}/status`, { status })
    return response.data
  },

  // Add order notes
  addOrderNote: async (id: string, note: string): Promise<{ message: string }> => {
    const response = await api.post(`/api/orders/${id}/notes`, { note })
    return response.data
  },

  // Process refund
  processRefund: async (id: string, amount: number, reason?: string): Promise<{ message: string }> => {
    const response = await api.post(`/api/orders/${id}/refund`, { amount, reason })
    return response.data
  },

  // Create shipment
  createShipment: async (id: string, shipmentData: any): Promise<{ message: string }> => {
    const response = await api.post(`/api/orders/${id}/shipments`, shipmentData)
    return response.data
  }
}

// Mock data for development - 50 orders
export const mockOrderData: OrderListItem[] = [
  {
    id: 'BC-001',
    customer_id: 'CUST-001',
    customer_name: 'John Smith',
    customer_email: '<EMAIL>',
    date_created: '2024-01-20T10:30:00Z',
    date_modified: '2024-01-20T14:45:00Z',
    status: 'Completed',
    status_id: 10,
    payment_status: 'Captured',
    payment_method: 'Credit Card',
    total_inc_tax: 299.99,
    items_total: 2,
    currency_code: 'USD',
    billing_address: {
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>'
    },
    shipping_addresses: [{
      first_name: 'John',
      last_name: 'Smith',
      city: 'New York',
      state: 'NY',
      country: 'United States'
    }],
    channel: 'bigcommerce'
  },
  {
    id: 'SH-002',
    customer_id: 'CUST-002',
    customer_name: 'Sarah Johnson',
    customer_email: '<EMAIL>',
    date_created: '2024-01-19T15:20:00Z',
    date_modified: '2024-01-20T09:15:00Z',
    status: 'Processing',
    status_id: 7,
    payment_status: 'Captured',
    payment_method: 'PayPal',
    total_inc_tax: 149.50,
    items_total: 1,
    currency_code: 'USD',
    billing_address: {
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>'
    },
    shipping_addresses: [{
      first_name: 'Sarah',
      last_name: 'Johnson',
      city: 'Los Angeles',
      state: 'CA',
      country: 'United States'
    }],
    channel: 'shopify'
  },
  // Generate remaining 48 orders
  ...Array.from({ length: 48 }, (_, i) => {
    const channels = ['bigcommerce', 'shopify', 'bigcommerce', 'shopify'] // Distribute orders across channels
    const channel = channels[i % channels.length]
    const channelPrefix = {
      'bigcommerce': 'BC',
      'shopify': 'SH',
      'amazon': 'AMZ',
      'ebay': 'EB'
    }[channel] || 'ORD'
    const id = `${channelPrefix}-${(i + 3).toString().padStart(3, '0')}`
    const customerNames = [
      'Michael Brown', 'Emily Davis', 'David Wilson', 'Lisa Anderson', 'Robert Taylor',
      'Jennifer Martinez', 'William Garcia', 'Jessica Rodriguez', 'James Lopez', 'Ashley Hernandez',
      'Christopher Lee', 'Amanda Gonzalez', 'Matthew Perez', 'Stephanie Turner', 'Joshua Phillips',
      'Michelle Campbell', 'Andrew Parker', 'Kimberly Evans', 'Daniel Edwards', 'Laura Collins',
      'Anthony Stewart', 'Rebecca Sanchez', 'Mark Morris', 'Sharon Rogers', 'Steven Reed',
      'Cynthia Cook', 'Paul Bailey', 'Kathleen Rivera', 'Kenneth Cooper', 'Amy Richardson',
      'Brian Cox', 'Angela Ward', 'Edward Torres', 'Brenda Peterson', 'Ronald Gray', 'Emma Ramirez',
      'Kevin James', 'Nicole Watson', 'Jason Brooks', 'Samantha Kelly', 'Jeffrey Sanders',
      'Rachel Price', 'Ryan Bennett', 'Heather Wood', 'Jacob Barnes', 'Megan Ross', 'Gary Henderson', 'Kayla Coleman'
    ]

    const statuses = ['Pending', 'Processing', 'Shipped', 'Completed', 'Cancelled', 'Refunded']
    const paymentStatuses = ['Pending', 'Captured', 'Partially Captured', 'Authorized', 'Partially Refunded', 'Refunded', 'Voided']
    const paymentMethods = ['Credit Card', 'PayPal', 'Apple Pay', 'Google Pay', 'Bank Transfer', 'Cash on Delivery']
    const cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose']
    const states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA', 'TX', 'CA', 'TX', 'CA']

    const customerName = customerNames[i] || `Customer ${i + 3}`
    const [firstName, lastName] = customerName.split(' ')
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`
    const status = statuses[i % statuses.length]
    const paymentStatus = paymentStatuses[i % paymentStatuses.length]
    const cityIndex = i % cities.length

    return {
      id,
      customer_id: `CUST-${(i + 3).toString().padStart(3, '0')}`,
      customer_name: customerName,
      customer_email: email,
      date_created: new Date(Date.now() - (i * 2) * 24 * 60 * 60 * 1000).toISOString(),
      date_modified: new Date(Date.now() - (i * 1) * 24 * 60 * 60 * 1000).toISOString(),
      status,
      status_id: statuses.indexOf(status) + 1,
      payment_status: paymentStatus,
      payment_method: paymentMethods[i % paymentMethods.length],
      total_inc_tax: Math.round((((i + 1) * 50) + 20) * 100) / 100,
      items_total: (i % 5) + 1,
      currency_code: 'USD',
      billing_address: {
        first_name: firstName,
        last_name: lastName,
        email
      },
      shipping_addresses: [{
        first_name: firstName,
        last_name: lastName,
        city: cities[cityIndex],
        state: states[cityIndex],
        country: 'United States'
      }],
      channel: channels[i % channels.length]
    }
  })
]

// Mock API service for development
export const mockOrderApi = {
  getOrders: async (params?: OrderQueryParams): Promise<OrderListResult> => {
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

    let filteredOrders = [...mockOrderData]

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase()
      filteredOrders = filteredOrders.filter(order =>
        order.id.toLowerCase().includes(search) ||
        order.customer_name?.toLowerCase().includes(search) ||
        order.customer_email?.toLowerCase().includes(search)
      )
    }

    if (params?.status) {
      filteredOrders = filteredOrders.filter(order => order.status === params.status)
    }

    if (params?.payment_status) {
      filteredOrders = filteredOrders.filter(order => order.payment_status === params.payment_status)
    }

    if (params?.channel) {
      filteredOrders = filteredOrders.filter(order => order.channel === params.channel)
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof OrderListItem
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1

      filteredOrders.sort((a, b) => {
        const aVal = a[sortBy]
        const bVal = b[sortBy]

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder
        }

        return 0
      })
    }

    // Apply pagination
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    const paginatedOrders = filteredOrders.slice(offset, offset + limit)

    return {
      orders: paginatedOrders,
      total: filteredOrders.length,
      page,
      limit,
      total_pages: Math.ceil(filteredOrders.length / limit)
    }
  },

  getOrder: async (id: string): Promise<Order> => {
    await new Promise(resolve => setTimeout(resolve, 300))

    const orderItem = mockOrderData.find(o => o.id === id)
    if (!orderItem) {
      throw new Error('Order not found')
    }

    // Return a full order with all details
    return {
      ...orderItem,
      subtotal_ex_tax: orderItem.total_inc_tax * 0.9,
      subtotal_inc_tax: orderItem.total_inc_tax * 0.9,
      subtotal_tax: orderItem.total_inc_tax * 0.1,
      base_shipping_cost: 9.99,
      shipping_cost_ex_tax: 9.99,
      shipping_cost_inc_tax: 10.99,
      shipping_cost_tax: 1.00,
      shipping_cost_tax_class_id: 1,
      base_handling_cost: 0,
      handling_cost_ex_tax: 0,
      handling_cost_inc_tax: 0,
      handling_cost_tax: 0,
      handling_cost_tax_class_id: 0,
      base_wrapping_cost: 0,
      wrapping_cost_ex_tax: 0,
      wrapping_cost_inc_tax: 0,
      wrapping_cost_tax: 0,
      wrapping_cost_tax_class_id: 0,
      total_ex_tax: orderItem.total_inc_tax * 0.9,
      total_tax: orderItem.total_inc_tax * 0.1,
      items_shipped: orderItem.items_total,
      payment_provider_id: 'stripe',
      refunded_amount: 0,
      order_is_digital: false,
      store_credit_amount: 0,
      gift_certificate_amount: 0,
      ip_address: '*************',
      currency_id: 1,
      currency_exchange_rate: 1,
      default_currency_id: 1,
      default_currency_code: 'USD',
      staff_notes: '',
      customer_message: '',
      discount_amount: 0,
      coupon_discount: 0,
      shipping_address_count: 1,
      is_deleted: false,
      billing_address: {
        id: 'ADDR-001',
        first_name: orderItem.billing_address.first_name,
        last_name: orderItem.billing_address.last_name,
        company: '',
        street_1: '123 Main Street',
        street_2: '',
        city: orderItem.shipping_addresses[0].city,
        state: orderItem.shipping_addresses[0].state,
        zip: '12345',
        country: orderItem.shipping_addresses[0].country,
        country_iso2: 'US',
        phone: '******-123-4567',
        email: orderItem.customer_email
      },
      shipping_addresses: [{
        id: 'ADDR-002',
        first_name: orderItem.shipping_addresses[0].first_name,
        last_name: orderItem.shipping_addresses[0].last_name,
        company: '',
        street_1: '123 Main Street',
        street_2: '',
        city: orderItem.shipping_addresses[0].city,
        state: orderItem.shipping_addresses[0].state,
        zip: '12345',
        country: orderItem.shipping_addresses[0].country,
        country_iso2: 'US',
        phone: '******-123-4567',
        email: orderItem.customer_email
      }],
      products: Array.from({ length: orderItem.items_total }, (_, i) => ({
        id: `PROD-${i + 1}`,
        order_id: orderItem.id,
        product_id: `${i + 1}`,
        name: `Product ${i + 1}`,
        sku: `SKU-${i + 1}`,
        type: 'physical' as const,
        base_price: 99.99,
        price_ex_tax: 99.99,
        price_inc_tax: 109.99,
        price_tax: 10.00,
        base_total: 99.99,
        total_ex_tax: 99.99,
        total_inc_tax: 109.99,
        total_tax: 10.00,
        quantity: 1,
        is_refunded: false,
        quantity_refunded: 0,
        refund_amount: 0
      }))
    } as Order
  }
}
